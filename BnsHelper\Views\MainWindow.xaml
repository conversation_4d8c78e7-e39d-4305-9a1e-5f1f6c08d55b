﻿<hc:Window x:Class="Xylia.BnsHelper.Views.MainWindow"
		xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:hc="https://handyorg.github.io/handycontrol"
		xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
		xmlns:system="clr-namespace:System;assembly=mscorlib"
		MinHeight="450" MinWidth="600" ResizeMode="CanMinimize" SizeToContent="WidthAndHeight"
		WindowStartupLocation="CenterScreen" Loaded="OnLoaded">
    
    <hc:Window.Title>
        <MultiBinding StringFormat="{}{0} {2}">
            <Binding Source="{x:Static helper:VersionHelper.ProductName}" />
            <Binding Source="{x:Static helper:VersionHelper.Version}" />
            <Binding Path="Setting.Server.Latency" FallbackValue="" TargetNullValue="" />
        </MultiBinding>
    </hc:Window.Title>

    <hc:Window.Resources>
        <system:String x:Key="Game">/Resources/Images/game.ico</system:String>
        <ImageSource x:Key="Head">pack://application:,,,/Resources/Images/head.png</ImageSource>
        <ImageSource x:Key="Logo">pack://application:,,,/Resources/Images/logo.png</ImageSource>
    </hc:Window.Resources>

    <!-- Commands -->
    <hc:Window.InputBindings>
        <KeyBinding Key="A" Modifiers="Ctrl" Command="hc:ControlCommands.PushMainWindow2Top" />
        <KeyBinding Key="B" Modifiers="Ctrl" Command="{Binding OpenClockAlarmPanelCommand}" />
        <KeyBinding Key="C" Modifiers="Ctrl" Command="{Binding OpenDamageMeterPanelCommand}" />
        <KeyBinding Key="R" Modifiers="Ctrl" Command="{Binding RandomHammerCommand}" />
        <KeyBinding Key="F4" Modifiers="Alt" Command="{Binding ShutdownAppCommand}" />
    </hc:Window.InputBindings>

    <hc:Window.NonClientAreaContent>
        <Grid>
            <!-- Menu -->
            <Menu HorizontalAlignment="Right" VerticalAlignment="Center">
                <MenuItem Header="&#xE713;" FontFamily="{DynamicResource SegoeAssets}" ToolTip="{DynamicResource Settings_Name}" Click="OpenSetting" />

                <MenuItem ToolTip="{DynamicResource Text.More}">
                    <MenuItem.Header>
                        <TextBlock Text="&#xE712;" FontFamily="{DynamicResource SegoeAssets}" />
                    </MenuItem.Header>
                    
                    <MenuItem Header="{DynamicResource MainWindow_OpenLink2}" Command="hc:ControlCommands.OpenLink" CommandParameter="https://bns.qq.com/neo/">
                        <MenuItem.Icon>
                            <Image Source="{Binding Source={StaticResource Game},Converter={StaticResource ImageSelector},ConverterParameter=16}" />
                        </MenuItem.Icon>
                    </MenuItem>
                    <MenuItem Header="{DynamicResource MainWindow_OpenLink1}" Command="hc:ControlCommands.OpenLink" CommandParameter="https://www.bnszs.com/">
                        <MenuItem.Icon>
                            <Image Source="{StaticResource Logo}" Width="16" Height="16" />
                        </MenuItem.Icon>
                    </MenuItem>

                    <!-- 用户信息 -->
                    <MenuItem Visibility="{Binding IsLogin,Converter={StaticResource Boolean2VisibilityConverter}}">
                        <MenuItem.Icon>
                            <Viewbox Width="16" Height="16">
                                <Path Data="{StaticResource UserGeometry}" Fill="{DynamicResource PrimaryBrush}" />
                            </Viewbox>
                        </MenuItem.Icon>
                        <MenuItem.Header>
                            <TextBlock>
								<Run Text="{DynamicResource MainWindow_OnlineUserCount}" />
								<Run Text="{Binding UserCount}" />
                            </TextBlock>
                        </MenuItem.Header>
                    </MenuItem>
                </MenuItem>
            </Menu>

            <!-- NotifyIcon -->
            <hc:NotifyIcon Token="NotifyIcon" IsBlink="{Binding NotifyIconIsBlink}" Visibility="{Binding Setting.UseNotifyIcon,Converter={StaticResource Boolean2VisibilityConverter}}">
                <hc:NotifyIcon.ContextMenu>
                    <ContextMenu>
                        <MenuItem Command="hc:ControlCommands.PushMainWindow2Top" Header="{DynamicResource Application_Push}" InputGestureText="Ctrl + A" />
                        <MenuItem Command="{Binding OpenClockAlarmPanelCommand}" Header="{DynamicResource Application_PushClock}" InputGestureText="Ctrl + B" />
                        <MenuItem Command="{Binding OpenDamageMeterPanelCommand}" Header="{DynamicResource Application_PushACT}" InputGestureText="Ctrl + C" />
                        <MenuItem Command="{Binding RandomHammerCommand}" Header="{DynamicResource Application_RandomHammer}" InputGestureText="Ctrl + R" />
                        <MenuItem Command="{Binding ShutdownAppCommand}" Header="{DynamicResource Application_Exit}" />
                    </ContextMenu>
                </hc:NotifyIcon.ContextMenu>
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="Click">
                        <hc:EventToCommand Command="hc:ControlCommands.PushMainWindow2Top" />
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
            </hc:NotifyIcon>
        </Grid>
    </hc:Window.NonClientAreaContent>

    <!-- Content -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="105" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- Slider -->
        <Grid Grid.RowSpan="99">
            <Grid.RowDefinitions>
                <RowDefinition Height="80" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Login -->
            <Button Grid.Row="0" Command="{Binding LoginCommand}" Style="{StaticResource ButtonCustom}">
                <Grid Width="60" Height="60">
                    <!-- 主头像 -->
                    <Image Source="{Binding User.HeadImg, Mode=OneWay, FallbackValue={StaticResource Head}}"
                           Width="50" Height="50"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                        <Image.Clip>
                            <RectangleGeometry RadiusX="25" RadiusY="25" Rect="0,0,50,50" />
                        </Image.Clip>
                        <!-- 头像右键菜单 -->
                        <Image.ContextMenu>
                            <ContextMenu Visibility="{Binding IsLogin, Converter={StaticResource Boolean2VisibilityConverter}}">
                                <!-- 权限级别 -->
                                <MenuItem IsEnabled="False">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource UserGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <Run Text="{Binding User.PermissionText,Mode=OneWay}" /> 
                                            <Run Text="{Binding User.ExpirationText,Mode=OneWay}" />
                                        </TextBlock>
                                    </MenuItem.Header>
                                </MenuItem>

                                <!-- 活动提示 -->
                                <MenuItem IsEnabled="False"
                                         Visibility="{Binding User.ShowActivityNotice, Converter={StaticResource Boolean2VisibilityConverter}}"
                                         Foreground="Orange" FontWeight="Bold">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource InfoGeometry}" Fill="Orange" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <Run Text="{DynamicResource MainWindow_BattleStatsPromo}" />
                                            <LineBreak />
                                            <Run Text="{DynamicResource MainWindow_SignInRestore}" />
                                        </TextBlock>
                                    </MenuItem.Header>
                                </MenuItem>

                                <Separator />

                                <!-- 总签到次数 -->
                                <MenuItem IsEnabled="False">
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <Run Text="{DynamicResource MainWindow_TotalSignIn}"/>
                                            <Run Text="{Binding User.TotalSignCount, Mode=OneWay}" FontWeight="Bold"/>
                                        </TextBlock>
                                    </MenuItem.Header>
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource ChartBarGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>

                                <!-- 签到按钮 -->
                                <MenuItem Command="{Binding SignInCommand}">
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Text" Value="{DynamicResource MainWindow_SignInNow}"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding User.IsSignedToday, Mode=OneWay}" Value="True">
                                                            <Setter Property="Text" Value="{DynamicResource MainWindow_SignedToday}"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding User.IsSignInLoading, Mode=OneWay}" Value="True">
                                                            <Setter Property="Text" Value="{DynamicResource MainWindow_SigningIn}"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </MenuItem.Header>
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource CalendarGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>

                                <!-- 口令码 -->
                                <MenuItem Command="{Binding ActivateCDKeyCommand}" Header="{DynamicResource MainWindow_ActivateCDKey}">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource SettingGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                
                                <Separator/>

                                <!-- 退出账号 -->
                                <MenuItem Command="{Binding LoginCommand}" Header="{DynamicResource MainWindow_Logout}" Foreground="{StaticResource DangerBrush}">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource UserGeometry}" Fill="{DynamicResource DangerBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </Image.ContextMenu>
                    </Image>

                    <!-- 权限标识 -->
                    <Grid Width="18" Height="18"
                          HorizontalAlignment="Right"
                          VerticalAlignment="Bottom"
                          Margin="0,0,5,5">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Setter Property="Visibility" Value="Collapsed" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="1">
                                        <Setter Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="2">
                                        <Setter Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>

                        <!-- 外层阴影圆 -->
                        <Ellipse Fill="#40000000" Width="18" Height="18" />

                        <!-- 白色背景圆 -->
                        <Ellipse Fill="White" Width="16" Height="16" />

                        <!-- 权限颜色圆 -->
                        <Ellipse Width="15" Height="15">
                            <Ellipse.Style>
                                <Style TargetType="Ellipse">
                                    <Setter Property="Fill">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                <GradientStop Color="#FFD700" Offset="0" />
                                                <GradientStop Color="#FFA500" Offset="1" />
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="1">
                                            <Setter Property="Fill">
                                                <Setter.Value>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                        <GradientStop Color="#FFD700" Offset="0" />
                                                        <GradientStop Color="#FFA500" Offset="1" />
                                                    </LinearGradientBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="2">
                                            <Setter Property="Fill">
                                                <Setter.Value>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                        <GradientStop Color="#9932CC" Offset="0" />
                                                        <GradientStop Color="#6A0DAD" Offset="1" />
                                                    </LinearGradientBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>

                        <!-- V字符标识 -->
                        <TextBlock Text="V"
                                   Foreground="White"
                                   FontSize="10"
                                   FontWeight="Bold"
                                   FontFamily="Arial"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Margin="0,0,0,1">
                            <TextBlock.Effect>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="0.5"
                                                  BlurRadius="1"
                                                  Opacity="0.8" />
                            </TextBlock.Effect>
                        </TextBlock>
                    </Grid>
                </Grid>
            </Button>

            <!-- Navigation -->
            <ListBox Grid.Row="2" x:Name="Navigation" Background="{DynamicResource SecondaryRegionBrush}" BorderThickness="0" SelectionChanged="Navigation_SelectionChanged">
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxItemCustom}">
                        <Setter Property="Cursor" Value="Hand" />
                        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        <Setter Property="Margin" Value="0 0 1 2" />
                    </Style>
                </ListBox.ItemContainerStyle>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Border Name="BorderRoot" MinHeight="38" Background="Transparent">
                            <hc:SimplePanel>
                                <Rectangle x:Name="Rect" Width="3" HorizontalAlignment="Left" />
                                <DockPanel Margin="13 0 0 0">
                                    <hc:Badge Status="Dot" BadgeMargin="0,7,-5,0" Style="{StaticResource BadgeDanger}" ShowBadge="{Binding IsNew}">
                                        <Path x:Name="PresenterIcon" Fill="{DynamicResource PrimaryTextBrush}" Data="{Binding Icon}" Width="18" Height="18" Stretch="Uniform" />
                                    </hc:Badge>
                                    <TextBlock x:Name="PresenterHeader" Text="{Binding Name}" Foreground="{DynamicResource PrimaryTextBrush}" FontSize="13" VerticalAlignment="Center" Margin="7 0" />
                                </DockPanel>
                            </hc:SimplePanel>
                        </Border>
                        <DataTemplate.Triggers>
                            <DataTrigger Binding="{Binding IsSelected,RelativeSource={RelativeSource AncestorType=ListBoxItem}}" Value="True">
                                <Setter TargetName="BorderRoot" Property="Background" Value="{DynamicResource BorderBrush}" />
                                <Setter TargetName="Rect" Property="Fill" Value="{DynamicResource PrimaryBrush}" />
                                <Setter TargetName="PresenterIcon" Property="Fill" Value="{DynamicResource PrimaryBrush}" />
                                <Setter TargetName="PresenterHeader" Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
                            </DataTrigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsMouseOver" Value="True" />
                                    <Condition Property="IsEnabled" Value="True"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="BorderRoot" Property="Background" Value="{DynamicResource BorderBrush}" />
                            </MultiTrigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="BorderRoot" Property="TextElement.Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                            </Trigger>
                        </DataTemplate.Triggers>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Grid>

        <!-- Content -->
        <Frame Grid.Column="1" x:Name="Presenter" BorderThickness="0" NavigationUIVisibility="Hidden" />

        <StackPanel Grid.Column="1" hc:Growl.GrowlParent="True" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,0,0,10" Panel.ZIndex="99" />
    </Grid>
</hc:Window>