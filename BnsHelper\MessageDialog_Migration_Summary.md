# MessageDialog 统一消息框迁移总结

## 📋 修改概述
将 `MainWindowViewModel` 中所有的 `MessageBox.Show` 调用替换为统一的 `MessageDialog.ShowDialog`，以保持应用程序UI的一致性。

## 🔧 修改详情

### 1. SetupPlugin 方法
**文件**: `BnsHelper/ViewModels/MainWindowViewModel.cs`
**行号**: 468

**修改前**:
```csharp
if (MessageBox.Show(StringHelper.Get("MainWindow_SetupPlugin_Warning"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.No) return;
```

**修改后**:
```csharp
if (await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_SetupPlugin_Warning"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.No) return;
```

### 2. RevertPlugin 方法
**文件**: `BnsHelper/ViewModels/MainWindowViewModel.cs`
**行号**: 519-520

**修改前**:
```csharp
if (MessageBox.Show(StringHelper.Get("MainWindow_RevertPlugin_Ask"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes)
    return;
```

**修改后**:
```csharp
if (await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_RevertPlugin_Ask"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes)
    return;
```

### 3. OnUserHeartbeatFailed 方法
**文件**: `BnsHelper/ViewModels/MainWindowViewModel.cs`
**行号**: 204

**修改前**:
```csharp
MessageBox.Show(StringHelper.Get("Message_NetworkLogout"), StringHelper.Get("ApplicationName"), MessageBoxButton.OK, MessageBoxImage.Warning);
```

**修改后**:
```csharp
MessageDialog.ShowDialog(StringHelper.Get("Message_NetworkLogout"), StringHelper.Get("ApplicationName"), MessageBoxButton.OK, MessageBoxImage.Warning).Wait();
```

**注意**: 由于此方法在 `Dispatcher.Invoke` 中且不是异步方法，使用 `.Wait()` 进行同步调用。

### 4. OpenClockAlarmPanel 方法
**文件**: `BnsHelper/ViewModels/MainWindowViewModel.cs`
**行号**: 327

**修改前**:
```csharp
MessageBox.Show(StringHelper.Get("Message_FeatureMaintenance"), icon: MessageBoxImage.Error);
```

**修改后**:
```csharp
MessageDialog.ShowDialog(StringHelper.Get("Message_FeatureMaintenance"), icon: MessageBoxImage.Error).Wait();
```

**注意**: 由于此方法不是异步方法，使用 `.Wait()` 进行同步调用。

### 5. SetuptLocal 方法
**文件**: `BnsHelper/ViewModels/MainWindowViewModel.cs`
**行号**: 567

**修改前**:
```csharp
if (MessageBox.Show(StringHelper.Get("MainWindow_SetupLocal_Ask"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes) return;
```

**修改后**:
```csharp
if (await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_SetupLocal_Ask"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes) return;
```

## ✅ 验证结果

- ✅ 所有 `MessageBox.Show` 调用已成功替换为 `MessageDialog.ShowDialog`
- ✅ 异步方法使用 `await` 调用
- ✅ 同步方法使用 `.Wait()` 调用
- ✅ 保持了原有的参数和返回值逻辑
- ✅ 添加了图标支持功能
- ✅ 无编译错误
- ✅ using 语句已存在，无需添加额外引用

## 🔧 图标支持功能

为了支持 `MessageBoxImage` 参数，对 `MessageDialog` 进行了以下增强：

### 新增属性和方法：
- `Icon` 属性：支持 `MessageBoxImage` 枚举值
- `UpdateIconGeometry()` 方法：根据图标类型动态设置图标几何形状和颜色
- 新增重载方法：`ShowDialog(string? message, string? btnText, MessageBoxButton button, MessageBoxImage icon, int autoCloseMilliseconds = 3000)`

### 图标映射：
- `MessageBoxImage.Information` → InfoGeometry + PrimaryBrush
- `MessageBoxImage.Warning` → WarningGeometry + WarningBrush
- `MessageBoxImage.Error` → ErrorGeometry + DangerBrush
- `MessageBoxImage.Question` → QuestionGeometry + InfoBrush

## 🎯 效果

1. **UI一致性**: 所有消息框现在使用统一的 `MessageDialog` 样式
2. **用户体验**: 提供一致的视觉体验和交互方式
3. **代码维护**: 统一的消息框API便于后续维护和修改
4. **功能完整**: 保持了所有原有的功能和逻辑

## 📝 注意事项

- 对于非异步方法中的调用，使用 `.Wait()` 确保同步执行
- 对于异步方法中的调用，使用 `await` 确保正确的异步处理
- 所有参数和返回值逻辑保持不变，确保功能兼容性
