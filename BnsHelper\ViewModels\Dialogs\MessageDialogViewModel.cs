using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace Xylia.BnsHelper.ViewModels.Dialogs;

internal partial class MessageDialogViewModel : ObservableObject, IDialogResultable<MessageBoxResult>
{
    #region Fields
    private DispatcherTimer? _autoCloseTimer;
    private DateTime _startTime;
    #endregion

    #region Properties
    [ObservableProperty] private string? _message;
    [ObservableProperty] private MessageBoxButton _buttonType = MessageBoxButton.OK;
    [ObservableProperty] private MessageBoxImage _icon = MessageBoxImage.Information;
    [ObservableProperty] private int _autoCloseMilliseconds = 0;
    [ObservableProperty] private string _okText = "确定";

    // Button visibility properties
    [ObservableProperty] private bool _showOkButton;
    [ObservableProperty] private bool _showCancelButton;
    [ObservableProperty] private bool _showYesButton;
    [ObservableProperty] private bool _showNoButton;

    public MessageBoxResult Result { get; set; }
    public Action? CloseAction { get; set; }
    #endregion

    #region Constructor
    public MessageDialogViewModel()
    {
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
    }
    #endregion

    #region Commands
    [RelayCommand]
    private void Ok()
    {
        StopTimer();
        Result = MessageBoxResult.OK;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Cancel()
    {
        StopTimer();
        Result = MessageBoxResult.Cancel;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Yes()
    {
        StopTimer();
        Result = MessageBoxResult.Yes;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void No()
    {
        StopTimer();
        Result = MessageBoxResult.No;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    public void Close()
    {
        StopTimer();
        Result = MessageBoxResult.Cancel;
        CloseAction?.Invoke();
    }
    #endregion

    #region Methods
    public void Initialize()
    {
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
    }

    public void UpdateIcon(Border? iconContainer)
    {
        if (iconContainer == null) return;

        try
        {
            // 如果Application.Current为null，延迟执行
            if (Application.Current?.Resources == null)
            {
                Debug.WriteLine("[MessageDialogViewModel] Application.Current.Resources is null");
                return;
            }

            string geometryKey;
            string brushKey;

            switch (Icon)
            {
                case MessageBoxImage.Information:
                    geometryKey = "InfoGeometry";
                    brushKey = "PrimaryBrush";
                    break;
                case MessageBoxImage.Warning:
                    geometryKey = "WarningGeometry";
                    brushKey = "WarningBrush";
                    break;
                case MessageBoxImage.Error:
                    geometryKey = "ErrorGeometry";
                    brushKey = "DangerBrush";
                    break;
                case MessageBoxImage.Question:
                    geometryKey = "AskGeometry";
                    brushKey = "InfoBrush";
                    break;
                default:
                    geometryKey = "InfoGeometry";
                    brushKey = "PrimaryBrush";
                    break;
            }

            Debug.WriteLine($"[MessageDialogViewModel] UpdateIcon: Icon={Icon}, GeometryKey={geometryKey}, BrushKey={brushKey}");

            // 动态创建Path元素
            try
            {
                Geometry? geometry = null;
                Brush? brush = Brushes.Gray;

                // 获取几何形状
                if (Application.Current.Resources.Contains(geometryKey))
                {
                    var resource = Application.Current.Resources[geometryKey];
                    if (resource is Geometry geo)
                    {
                        geometry = geo;
                        Debug.WriteLine($"[MessageDialogViewModel] IconGeometry found successfully");
                    }
                    else
                    {
                        Debug.WriteLine($"[MessageDialogViewModel] Resource '{geometryKey}' is not a Geometry, type: {resource?.GetType()}");
                    }
                }
                else
                {
                    Debug.WriteLine($"[MessageDialogViewModel] Resource '{geometryKey}' not found");
                }

                // 获取画刷
                if (Application.Current.Resources.Contains(brushKey))
                {
                    var resource = Application.Current.Resources[brushKey];
                    if (resource is Brush br)
                    {
                        brush = br;
                        Debug.WriteLine($"[MessageDialogViewModel] IconBrush found successfully");
                    }
                    else
                    {
                        Debug.WriteLine($"[MessageDialogViewModel] Resource '{brushKey}' is not a Brush, type: {resource?.GetType()}");
                    }
                }
                else
                {
                    Debug.WriteLine($"[MessageDialogViewModel] Resource '{brushKey}' not found");
                }

                // 创建Path元素
                if (geometry != null)
                {
                    var path = new Path
                    {
                        Data = geometry,
                        Fill = brush,
                        Stretch = Stretch.Fill,
                        Width = 30,
                        Height = 30
                    };

                    iconContainer.Child = path;
                    Debug.WriteLine("[MessageDialogViewModel] Path element created and added successfully");
                }
                else
                {
                    // 如果没有找到几何形状，清空容器
                    iconContainer.Child = null;
                    Debug.WriteLine("[MessageDialogViewModel] No geometry found, icon container cleared");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[MessageDialogViewModel] Error creating Path element: {ex.Message}");
                iconContainer.Child = null;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[MessageDialogViewModel] UpdateIcon failed: {ex.Message}");
        }
    }

    private void UpdateButtonVisibility()
    {
        ShowOkButton = ButtonType == MessageBoxButton.OK || ButtonType == MessageBoxButton.OKCancel;
        ShowCancelButton = ButtonType == MessageBoxButton.OKCancel || ButtonType == MessageBoxButton.YesNoCancel;
        ShowYesButton = ButtonType == MessageBoxButton.YesNo || ButtonType == MessageBoxButton.YesNoCancel;
        ShowNoButton = ButtonType == MessageBoxButton.YesNo || ButtonType == MessageBoxButton.YesNoCancel;
    }

    private void UpdateAutoCloseTimer()
    {
        // 先停止现有的定时器
        StopTimer();

        // 只有当 AutoCloseMilliseconds > 0 时才启动新的定时器
        if (AutoCloseMilliseconds > 0)
        {
            StartAutoCloseTimer();
        }
    }

    private void StartAutoCloseTimer()
    {
        if (_autoCloseTimer != null) return; // 避免重复启动

        _startTime = DateTime.Now;
        _autoCloseTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(100) // 每100ms检查一次
        };
        _autoCloseTimer.Tick += OnTimerTick;
        _autoCloseTimer.Start();
    }

    private void OnTimerTick(object? sender, EventArgs e)
    {
        var elapsed = (DateTime.Now - _startTime).TotalMilliseconds;

        if (elapsed >= AutoCloseMilliseconds)
        {
            _autoCloseTimer?.Stop();
            Result = MessageBoxResult.OK; // Auto-close returns OK
            CloseAction?.Invoke();
        }
    }

    private void StopTimer()
    {
        _autoCloseTimer?.Stop();
        _autoCloseTimer = null;
    }
    #endregion

    #region Property Changed Handlers
    partial void OnButtonTypeChanged(MessageBoxButton value)
    {
        UpdateButtonVisibility();
    }

    partial void OnAutoCloseMillisecondsChanged(int value)
    {
        UpdateAutoCloseTimer();
    }
    #endregion
}
