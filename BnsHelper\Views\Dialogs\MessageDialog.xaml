﻿<Border x:Class="Xylia.Preview.UI.Views.Dialogs.MessageDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:hc="https://handyorg.github.io/handycontrol"
        Background="{DynamicResource RegionBrush}" CornerRadius="10"
		MaxWidth="420" MinHeight="135" Margin="75 0 0 30">

	<Grid Margin="7 10">
		<Grid.RowDefinitions>
			<RowDefinition Height="*" />
			<RowDefinition Height="Auto" />
		</Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Border x:Name="IconContainer" Width="30" Height="30" Margin="5 0 10 0" />
        <TextBlock d:Text="Message" Text="{Binding Message}" FontSize="16" VerticalAlignment="Center" Margin="5" TextWrapping="Wrap" Grid.Column="1" MinWidth="240" />

        <hc:UniformSpacingPanel Grid.Row="1" Grid.ColumnSpan="99" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Bottom" Spacing="10">
            <Button d:Content="取消" Content="{DynamicResource Text.Cancel}" Width="75" Height="30" Command="{Binding CancelCommand}"
					Visibility="{Binding ShowCancelButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
            <Button d:Content="否" Content="{DynamicResource Text.No}" Width="75" Height="30" Command="{Binding NoCommand}"
					Visibility="{Binding ShowNoButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
            <Button d:Content="是" Content="{DynamicResource Text.Yes}" Style="{StaticResource ButtonPrimary}" Width="75" Height="30" Command="{Binding YesCommand}"
					Visibility="{Binding ShowYesButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
            <Button d:Content="确定" Content="{Binding OkText}" Style="{StaticResource ButtonPrimary}" Width="75" Height="30" Command="{Binding OkCommand}"
					Visibility="{Binding ShowOkButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
		</hc:UniformSpacingPanel>
	</Grid>
</Border>
