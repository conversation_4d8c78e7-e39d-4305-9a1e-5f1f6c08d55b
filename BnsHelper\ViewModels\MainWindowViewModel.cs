﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Data;
using HandyControl.Tools.Extension;
using Microsoft.Win32;
using Serilog;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Windows;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Services.ApiEndpoints;
using Xylia.BnsHelper.Views;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.Properties;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels;
internal partial class MainWindowViewModel : ObservableObject
{
    #region Message
    [ObservableProperty] private bool _notifyIconIsBlink;
    [ObservableProperty] private bool _useAds = true;

    internal static readonly MainWindowViewModel Instance = new();
    private readonly AnnouncementService _announcementService;

    /// <summary>
    /// 构造函数
    /// </summary>
    private MainWindowViewModel()
    {
        // 监听User属性变化
        PropertyChanged += OnMainViewModelPropertyChanged;

        // 监听游戏目录变化，更新插件版本显示
        SettingHelper.Default.GameDirectoryChanged += (sender, e) =>
        {
            OnPropertyChanged(nameof(PluginVersion));
        };

        // 初始化公告服务
        _announcementService = AnnouncementService.Instance;
        _announcementService.Announcements.CollectionChanged += (s, e) =>
        {
            OnPropertyChanged(nameof(UnreadAnnouncementCount));
            OnPropertyChanged(nameof(HasUnreadAnnouncements));
        };
    }

    public static void SendBalloonTip(string? title, string? content = null, NotifyIconInfoType type = NotifyIconInfoType.None)
    {
        NotifyIcon.ShowBalloonTip(title, content ?? " ", type, "NotifyIcon");
    }
    #endregion

    #region Login
    /// <summary>
    /// 用户会话状态变化事件，用于通知UI层处理需要权限的分页
    /// </summary>
    public event EventHandler<bool>? SessionStateChanged;

    /// <summary>
    /// 战斗统计权限变化事件，用于通知UI层关闭战斗统计面板
    /// </summary>
    public event EventHandler? DamageMeterPermissionChanged;

    User? _user;
    public User? User
    {
        get => _user;
        internal set
        {
            // 清理旧用户的事件订阅和资源
            if (_user != null)
            {
                try
                {
                    Debug.WriteLine("[INFO] 清理旧用户资源");
                    _user.SessionStateChanged -= OnUserSessionStateChanged;
                    _user.HeartbeatFailed -= OnUserHeartbeatFailed;
                    _user.PropertyChanged -= OnUserPropertyChanged;

                    // 释放旧用户资源
                    _user.Dispose();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[ERROR] 清理旧用户资源时发生异常: {ex.Message}");
                }
            }

            SetProperty(ref _user, value);
            OnPropertyChanged(nameof(IsLogin));
            OnPropertyChanged(nameof(CanOpenDamageMeter));

            // 订阅新用户的事件
            if (_user != null)
            {
                _user.SessionStateChanged += OnUserSessionStateChanged;
                _user.HeartbeatFailed += OnUserHeartbeatFailed;
                _user.PropertyChanged += OnUserPropertyChanged;
            }

            OpenDamageMeterPanelCommand.NotifyCanExecuteChanged();

            // 触发会话状态变化事件
            SessionStateChanged?.Invoke(this, _user?.IsLoggedIn == true);
        }
    }

    public bool IsLogin => User?.IsLoggedIn == true;

    /// <summary>
    /// 是否可以打开战斗统计面板（需要登录且有高级权限）
    /// </summary>
    public bool CanOpenDamageMeter
    {
        get
        {
            var user = User;
            if (user == null || !user.IsLoggedIn) return false;

            // 检查用户权限是否满足要求（权限级别1表示有CDKey或会员权限）
            return user.Permission >= 1;
        }
    }

    [RelayCommand]
    internal async Task<bool?> Login()
    {
        // check login status
        if (IsLogin)
        {
            if (await MessageDialog.ShowDialog(
                StringHelper.Get("UserLogin_RequsetLogoutTip", User!.Uin),
                null, MessageBoxButton.OKCancel) == MessageBoxResult.Cancel) return true;

            // 清理登录状态
            await LogoutUserAsync();
        }

        OnPropertyChanged(nameof(IsLogin));
        return new UserLogin2().ShowDialog();
    }

    /// <summary>
    /// 异步注销用户
    /// </summary>
    internal async Task LogoutUserAsync()
    {
        if (User != null)
        {
            await User.LogoutAsync();
            User = null;
            UserCount = 0; // 重置在线用户数量
        }
    }

    /// <summary>
    /// 用户会话状态变化处理
    /// </summary>
    private void OnUserSessionStateChanged(object? sender, bool status)
    {
        // 在UI线程中更新UI状态
        Application.Current.Dispatcher.Invoke(() =>
        {
            OnPropertyChanged(nameof(IsLogin));
            OnPropertyChanged(nameof(CanOpenDamageMeter));

            // 通知命令状态更新
            OpenDamageMeterPanelCommand.NotifyCanExecuteChanged();
            SignInCommand.NotifyCanExecuteChanged();

            if (!status) UserCount = 0;

            // 通知UI层处理需要权限的分页
            SessionStateChanged?.Invoke(this, status);
        });
    }

    /// <summary>
    /// 用户心跳失败处理
    /// </summary>
    private void OnUserHeartbeatFailed(object? sender, EventArgs e)
    {
        Debug.WriteLine("[WARNING] 心跳失败，自动退出登录");

        Application.Current.Dispatcher.Invoke(() =>
        {
            User = null;

            // 显示通知
            SendBalloonTip(StringHelper.Get("Message_LoginExpired"), StringHelper.Get("Message_NetworkLogout"), NotifyIconInfoType.Warning);
            MessageDialog.ShowDialog(StringHelper.Get("Message_NetworkLogout"), null, MessageBoxButton.OK, MessageBoxImage.Warning).Wait();
        });
    }

    /// <summary>
    /// 用户属性变化处理
    /// </summary>
    private void OnUserPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(User.Permission):
                // 当用户权限发生变化时，更新相关属性
                Application.Current.Dispatcher.Invoke(() =>
                {
                    OnPropertyChanged(nameof(CanOpenDamageMeter));
                    OpenDamageMeterPanelCommand.NotifyCanExecuteChanged();

                    // 通知UI层检查战斗统计面板权限
                    DamageMeterPermissionChanged?.Invoke(this, EventArgs.Empty);
                });
                break;

            case nameof(User.CanSign):
                // 当User的签到相关属性发生变化时，更新相关的命令状态
                Application.Current.Dispatcher.Invoke(() =>
                {
                    SignInCommand.NotifyCanExecuteChanged();
                    OnPropertyChanged(nameof(CanSignIn));
                });
                break;
        }
    }

    /// <summary>
    /// MainWindowViewModel属性变化处理
    /// </summary>
    private void OnMainViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // 当User属性发生变化时，监听User的属性变化
        if (e.PropertyName == nameof(User))
        {
            // 监听新User的属性变化
            if (User != null)
            {
                User.PropertyChanged += OnUserPropertyChanged;
            }
        }
    }




    /// <summary>
    /// 是否可以签到
    /// </summary>
    public bool CanSignIn => IsLogin && User?.CanSign == true;

    /// <summary>
    /// 执行签到
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSignIn))]
    async Task SignIn()
    {
        ArgumentNullException.ThrowIfNull(User);

        var (success, message) = await User.SignInAsync();

        if (success)
        {
            // 显示消息框作为最终保障
            SendBalloonTip(StringHelper.Get("Message_SignInSuccess"), message, NotifyIconInfoType.Info);
            await MessageDialog.ShowDialog(message, StringHelper.Get("Dialog_SignIn"), MessageBoxButton.OK);
        }
        else
        {
            await MessageDialog.ShowDialog($"{StringHelper.Get("Message_SignInFailed")}\n{message}", StringHelper.Get("Dialog_SignIn"), MessageBoxButton.OK);
        }
    }

    /// <summary>
    /// 刷新签到状态
    /// </summary>
    [RelayCommand]
    async Task RefreshSignInStatus()
    {
        if (User != null)
        {
            await User.RefreshSignInStatusAsync();
        }
    }

    /// <summary>
    /// 激活CDKEY
    /// </summary>
    [RelayCommand]
    async Task ActivateCDKey()
    {
        if (await Dialog.Show<CDKeyDialog>().GetResultAsync<bool>())
        {
            // CDKEY激活成功，可能需要刷新用户权限或其他状态
            await User?.RefreshSignInStatusAsync();
        }
    }

    #endregion

    #region Common
    public SettingHelper Setting => SettingHelper.Default;

    [ObservableProperty] private int _userCount;
    [ObservableProperty] private bool _isRunning;
    public bool OnShutdown = false;

    [RelayCommand(CanExecute = nameof(CanOpenDamageMeter))]
    void OpenDamageMeterPanel()
    {
        DamageMeterPanel.Instance.Show();
    }

    [RelayCommand]
    void OpenClockAlarmPanel()
    {
        MessageBox.Show(StringHelper.Get("Message_FeatureMaintenance"), icon: MessageBoxImage.Error);
        // new ClockAlarmPanel().Show();
    }

    [RelayCommand]
    void ShutdownApp()
    {
        OnShutdown = true;
        Application.Current.Shutdown();
    }

    /// <summary>
    /// 未读公告数量
    /// </summary>
    public int UnreadAnnouncementCount => _announcementService.UnreadCount;

    /// <summary>
    /// 是否有未读公告
    /// </summary>
    public bool HasUnreadAnnouncements => UnreadAnnouncementCount > 0;

    /// <summary>
    /// 显示公告窗口
    /// </summary>
    [RelayCommand]
    void ShowAnnouncements()
    {
        var window = new AnnouncementWindow
        {
            Owner = Application.Current.MainWindow
        };
        window.ShowDialog();

        // 刷新未读数量
        OnPropertyChanged(nameof(UnreadAnnouncementCount));
        OnPropertyChanged(nameof(HasUnreadAnnouncements));
    }
    #endregion

    #region Game
    /// <summary>
    /// 获取用户存储数据
    /// </summary>
    public BnsSaved? AutoSaved => BnsSaved.Get(() => OnPropertyChanged(nameof(AutoSaved)));

    public string? PluginVersion => GetPluginVersion();

    private static string ClientPath => Path.Combine(SettingHelper.Default.Game.FullPath, "Binaries\\Win64\\BNSR.exe");

    internal static string PluginPath => Path.Combine(SettingHelper.Default.Game.FullPath, "Binaries\\Win64\\libiconv2017_cl64.dll");

    /// <summary>
    /// 获取插件版本
    /// </summary>
    /// <returns></returns>
    public static string? GetPluginVersion()
    {
        try
        {
            if (File.Exists(PluginPath))
            {
                var info = FileVersionInfo.GetVersionInfo(PluginPath);
                if (info.InternalName == "bnszs") return info.FileVersion;
            }

            return StringHelper.Get("MainWindow_NoPlugin");
        }
        catch (AppException)
        {
            return StringHelper.Get("MainWindow_NoPlugin");
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    /// <summary>
    /// 生成安全密钥
    /// </summary>
    /// <param name="version">版本号</param>
    /// <param name="permission">用户权限级别</param>
    /// <returns></returns>
    private static string GenerateSecretKey(string version, int permission = 0)
    {
        // 使用版本号、权限级别和固定盐值生成密钥，统一使用Unicode编码
        var baseKey = $"BnsZs_SecureKey_{version}_P{permission}_2024";
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var keyBytes = sha256.ComputeHash(System.Text.Encoding.Unicode.GetBytes(baseKey));
        return Convert.ToBase64String(keyBytes);
    }

    /// <summary>
    /// 检查插件版本
    /// </summary>
    /// <param name="required"></param>
    public static void CheckPlugin(Version? required = null)
    {
        if (!Version.TryParse(GetPluginVersion(), out var version)) throw new AppException(ExceptionCode.InvalidPlugin);
        else if (required != null && required > version) throw new AppException(ExceptionCode.InvalidPluginVersion);
        else if (Application.Current.Properties["PluginVersion"].To<Version>() > version) throw new WarningException(StringHelper.Get("MainWindow_NewPlugin"));
    }

    /// <summary>
    /// 请求游戏重新加载配置
    /// </summary>
    public static void ReloadConfig()
    {
        User32.SendMessage(WindowHelper.GetGameWindow(), User32.WindowMessage.WM_SYSKEYDOWN, User32.VK.VK_INSERT);
    }

    /// <summary>
    /// 安装或更新插件
    /// </summary>
    /// <returns></returns>
    [RelayCommand]
    async Task SetupPlugin()
    {
        // 检查登录状态，未登录时提示需要登录
        if (!IsLogin)
        {
            if (await MessageDialog.ShowDialog(
                StringHelper.Get("UserLogin_RequsetLoginTip1"),
                StringHelper.Get("UserLogin_RequsetLogin"), MessageBoxButton.OKCancel) != MessageBoxResult.OK) return;

            // 判断是否登录成功
            if (await Login() == false) return;
        }

        ArgumentNullException.ThrowIfNull(User);

        try
        {
            IsRunning = true;

            var path = PluginPath;
            if (!Directory.GetParent(PluginPath)?.Exists ?? true) throw new AppException(ExceptionCode.InvalidGame);

            // backup if is original
            if (File.Exists(path) && FileVersionInfo.GetVersionInfo(path).InternalName != "bnszs")
            {
                if (await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_SetupPlugin_Warning"), null, MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.No) return;
                File.Copy(path, path + "_bak", true);
            }

            // download plugin if different
            var LastestVersion = Application.Current.Properties["PluginVersion"].To<string>();
            if (LastestVersion == PluginVersion) await Task.Delay(2000);
            else
            {
                var stream = await HttpHelper.DownloadAsync(Application.Current.Properties["PluginUrl"].To<string>());
                await stream.SaveAsync(path);
            }

            // register path
            using RegistryKey hklm = Registry.LocalMachine;
            using RegistryKey directory = hklm.CreateSubKey($@"Software\Xylia\bns-plugins\directory", true);

            // 使用HMAC-SHA256签名替代MD5，并包含用户权限信息
            var secretKey = GenerateSecretKey(LastestVersion, User!.Permission);
            var signature = ClientPath.MD5Sign(secretKey)!;
            directory.SetValue(ClientPath, signature, RegistryValueKind.String);

            // update message
            OnPropertyChanged(nameof(PluginVersion));
            await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_SetupPlugin_Message"));
        }
        catch (Exception ex)
        {
            Log.Error(ex, "SetupPlugin");
            await MessageDialog.ShowDialog(ex switch
            {
                HttpRequestException => StringHelper.Get("MainWindow_SetupPlugin_Failed2"),
                IOException => StringHelper.Get("MainWindow_SetupPlugin_Failed3"),
                _ => StringHelper.Get("MainWindow_SetupPlugin_Failed", ex.Message)
            }, null, -1);
        }
        finally
        {
            IsRunning = false;
        }
    }



    /// <summary>
    /// 卸载插件
    /// </summary>
    [RelayCommand]
    async Task RevertPlugin()
    {
        var path = PluginPath;
        if (await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_RevertPlugin_Ask"), null, MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes)
            return;

        try
        {
            // 如果存在备份，直接恢复备份
            var bakPath = path + "_bak";
            if (File.Exists(bakPath)) File.Copy(bakPath, path, true);
            else
            {
                var stream = await HttpHelper.DownloadAsync("https://gitee.com/XyliaUp/tools/releases/download/backup/libiconv2017_cl64.dll");
                await stream.SaveAsync(path);
            }

            OnPropertyChanged(nameof(PluginVersion));
            await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_RevertPlugin_Message"));
        }
        catch (Exception ex)
        {
            await MessageDialog.ShowDialog(ex switch
            {
                IOException => StringHelper.Get("Message_PathAccessDenied"),
                _ => StringHelper.Get("MainWindow_RevertPlugin_Failed", ex.Message)
            }, null, -1);
        }
    }

    /// <summary>
    /// 反和谐
    /// </summary>
    [RelayCommand]
    async Task SetuptLocal()
    {
        var root = SettingHelper.Default.Game;
        var setting = new IniSettings(Path.Combine(root.FullPath, "Binaries\\Win64\\local.ini"));
        File.SetAttributes(setting.ConfigPath, FileAttributes.Normal);

        var Publisher = setting.GetValue(EPublisher.Invalid, "Publisher", "Locale");
        var DataPublisher = setting.GetValue(EDataPublisher.NONE, "DataPublisher", "Locale");
        var Language = setting.GetValue(ELanguage.ChineseS, "Language", "Locale");

        // check publisher
        if (Publisher == EPublisher.ZTX ||
           (Publisher == EPublisher.ZNCS && DataPublisher == EDataPublisher.TX))
        {
            if (Language == ELanguage.ChineseS)
            {
                if (await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_SetupLocal_Ask"), null, MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes) return;

                setting.SetValue(ELanguage.ChineseT.ToString(), "Language", "Locale");

                // copy splash
                var splash1 = Path.Combine(root.FullPath, $@"Content\local\{Publisher}\ChineseS\Splash\Splash.bmp");
                var splash2 = Path.Combine(root.FullPath, $@"Content\local\{Publisher}\ChineseT\Splash\Splash.bmp");

                Directory.CreateDirectory(Path.GetDirectoryName(splash2)!);
                File.Copy(splash1, splash2, true);
                File.SetAttributes(setting.ConfigPath, FileAttributes.ReadOnly);

                await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_SetupLocal_Message"));
            }
            else
            {
                setting.SetValue(ELanguage.ChineseS.ToString(), "Language", "Locale");
                File.SetAttributes(setting.ConfigPath, FileAttributes.Normal);

                await MessageDialog.ShowDialog(StringHelper.Get("MainWindow_RevertLocal_Message"));
            }
        }
        else
        {
            throw new AppException(ExceptionCode.InvalidPublisher2, EPublisher.ZTX);
        }
    }

    /// <summary>
    /// 开始游戏
    /// </summary>
    [RelayCommand]
    public void StartGame()
    {
        // 限制用户进行多开
        var path = Path.Combine(SettingHelper.Default.Game.FullPath, "Binaries\\Win64\\", "BNSR.exe");
        var instance = ProcessExtensions.Find(path).FirstOrDefault();
        if (instance != null)
        {
            User32.SetForegroundWindow(instance!.MainWindowHandle);
            return;
        }

        // 验证服务器类型
        var server = SettingHelper.Default.Server;
        if (server is null || !(server.Publisher is EPublisher.ZTX or EPublisher.ZNCG)) throw new NotSupportedException();

        // 从进程列表中获取启动参数
        var process = ProcessExtensions.GetCommandLineArgs(server == BnsApiEndpoint.Ztx90 ? "downloader_hdiff" : "downloader").Where(x => x.Contains("--game"));
        if (process is null || !process.Any()) throw new WarningException(StringHelper.Get("MainWindow_StartGame_Message"));

        var args = process.First().Split(" ", StringSplitOptions.RemoveEmptyEntries);
        var commandLineArgs = new List<string>() { "-StartFromLauncher", "--maple-url " + server.Map };
        for (int i = 0; i < args.Length; i++)
        {
            var arg = args[i];
            if (arg == "--game") commandLineArgs.Add("--game-id " + args[i + 1]);
            if (arg == "--channel") commandLineArgs.Add("--game-chid " + args[i + 1]);
            if (arg == "--port") commandLineArgs.Add("--port " + args[i + 1]);
        }

        Process.Start(path, string.Join(' ', commandLineArgs));
    }

    /// <summary>
    /// 随机成长
    /// </summary>
    [RelayCommand]
    async Task RandomHammer()
    {
        await AudioHelper.Play($"Xylia.BnsHelper.Resources.Musics.Signal_ItemTransform_Progress.mp3");

        var final = RandomHelper.Get([0.8f, 0.2f]);
        if (final == 0)
        {
            await AudioHelper.Play($"Xylia.BnsHelper.Resources.Musics.Signal_ItemTransform_Fail.mp3");
        }
        else
        {
            await AudioHelper.Play($"Xylia.BnsHelper.Resources.Musics.Signal_ItemTransform_Success02.mp3");
        }
    }
    #endregion
}
