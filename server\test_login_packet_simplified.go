package main

import (
	"fmt"
	"log"

	"github.com/bnspatch/server/pkg/binary"
)

func main() {
	fmt.Println("=== 登录响应包简化测试 ===")

	// 创建网络处理器
	networkHandler := binary.NewNetworkHandler()

	// 测试成功响应（只包含必要字段）
	fmt.Println("\n1. 测试成功响应（简化字段）:")
	successData := map[string]interface{}{
		"uid":                   uint64(12345),
		"token":                 "test-token-123",
		"permission":            uint8(1),
		"permission_expiration": int64(1735689600), // 2025-01-01
	}

	successResponse, err := networkHandler.CreateSuccessResponse(binary.MsgTypeLoginResponse, successData)
	if err != nil {
		log.Fatalf("创建成功响应失败: %v", err)
	}

	fmt.Printf("成功响应包大小: %d 字节\n", len(successResponse))

	// 解析响应包验证内容
	message, err := networkHandler.ParseMessage(successResponse)
	if err != nil {
		log.Fatalf("解析响应包失败: %v", err)
	}

	fmt.Printf("消息类型: 0x%02X\n", message.Header.MsgType)
	fmt.Printf("消息体大小: %d 字节\n", len(message.Body))

	// 测试错误响应
	fmt.Println("\n2. 测试错误响应:")
	errorResponse, err := networkHandler.CreateErrorResponse(1001, "测试错误消息")
	if err != nil {
		log.Fatalf("创建错误响应失败: %v", err)
	}

	fmt.Printf("错误响应包大小: %d 字节\n", len(errorResponse))

	// 解析错误响应包
	errorMessage, err := networkHandler.ParseMessage(errorResponse)
	if err != nil {
		log.Fatalf("解析错误响应包失败: %v", err)
	}

	fmt.Printf("错误消息类型: 0x%02X\n", errorMessage.Header.MsgType)
	fmt.Printf("错误消息体大小: %d 字节\n", len(errorMessage.Body))

	fmt.Println("\n✅ 登录响应包简化测试完成")
	fmt.Println("现在登录响应只包含以下字段:")
	fmt.Println("- ErrorCode (uint32)")
	fmt.Println("- ErrorMsg (string, 仅错误时)")
	fmt.Println("- Token (string)")
	fmt.Println("- UID (uint64)")
	fmt.Println("- Permission (uint8)")
	fmt.Println("- PermissionExpiration (int64)")
	fmt.Println("\n已移除的字段:")
	fmt.Println("- UIN (string)")
	fmt.Println("- Name (string)")
	fmt.Println("- Email (string)")
	fmt.Println("- Status (uint32)")
	fmt.Println("- Beta (bool)")
	fmt.Println("- LoginTime (uint64)")
	fmt.Println("- TokenTime (uint64)")
}
