package binary

import (
	"encoding/binary"
	"fmt"
)

// 登录请求消息体结构
type LoginRequest struct {
	QQNumber          string // QQ号
	DeviceFingerprint string // 设备指纹（客户端计算，服务端根据此生成设备码）
}

// 登录响应消息体结构
// 字段顺序：ErrorCode + [ErrorMsg if ErrorCode != 0] + [业务数据 if ErrorCode == 0]
// ErrorCode=0表示成功，非0表示错误
// 成功时包含业务数据：Token, UID, Permission, PermissionExpiration
// 失败时只包含ErrorMsg
// 注意：UIN, Name, Email, Status, Beta, LoginTime, TokenTime字段保留在结构体中但不编码传输
type LoginResponse struct {
	ErrorCode            uint32 // 错误码
	ErrorMsg             string // 错误消息（仅在ErrorCode != 0时包含）
	Token                string // Token
	UID                  uint64 // 用户ID
	UIN                  string // UIN
	Name                 string // 用户名
	Email                string // 邮箱
	Status               uint32 // 状态
	Beta                 bool   // Beta标志
	Permission           uint8  // 用户最终权限：0-普通用户，1-高级用户，2-会员用户（服务端计算最终值）
	PermissionExpiration int64  // 权限过期时间：-1=永久，0=无权限，>0=具体时间戳
	LoginTime            uint64 // 登录时间
	TokenTime            uint64 // Token时间
}

// 心跳请求消息体结构
type HeartbeatRequest struct {
	Token string // Token
}

// 注销请求消息体结构
type LogoutRequest struct {
	Token string // Token
}

// 注销响应消息体结构
// 字段顺序：ErrorCode + [ErrorMsg if ErrorCode != 0]
// ErrorCode=0表示成功，非0表示错误
type LogoutResponse struct {
	ErrorCode uint32 // 错误码
	ErrorMsg  string // 错误消息（仅在ErrorCode != 0时包含）
}

// 签到抽奖请求消息体结构
type LuckyDrawRequest struct {
	Token string // Token
}

// 签到抽奖响应消息体结构
// 字段顺序：ErrorCode + [ErrorMsg if ErrorCode != 0] + [业务数据 if ErrorCode == 0]
// ErrorCode=0表示成功，非0表示错误
// 成功时包含业务数据：Message, Count, Permission（可选）, PermissionExpiration（可选）
// 失败时只包含ErrorMsg
type LuckyDrawResponse struct {
	ErrorCode            uint32 // 错误码
	ErrorMsg             string // 错误消息（仅在ErrorCode != 0时包含）
	Message              string // 抽奖结果消息
	Count                uint32 // 额外次数
	Permission           *uint8 // 更新后的权限级别（仅在权限有变化时包含）
	PermissionExpiration *int64 // 更新后的权限过期时间（仅在权限有变化时包含）：-1=永久，0=无权限，>0=具体时间戳
}

// 获取签到状态请求消息体结构
type LuckyStatusRequest struct {
	Token string // Token
}

// 签到状态响应消息体结构（简化版）
// 字段顺序：ErrorCode + [ErrorMsg if ErrorCode != 0] + [业务数据 if ErrorCode == 0]
// ErrorCode=0表示成功，非0表示错误
// 成功时包含业务数据：TotalDays, AvailableCount
// 失败时只包含ErrorMsg
type LuckyStatusResponse struct {
	ErrorCode      uint32 // 错误码
	ErrorMsg       string // 错误消息（仅在ErrorCode != 0时包含）
	TotalDays      uint32 // 签到总天数
	AvailableCount uint32 // 今日可用次数（今日次数+额外次数）
}

// CDKEY激活请求消息体结构
type CDKeyActivateRequest struct {
	Token      string // Token
	CDKey      string // CDKEY字符串
	IsVerified bool   // 是否已验证群成员身份
}

// CDKeyActivateResponse CDKEY激活响应消息体结构
// 字段顺序：ErrorCode + [ErrorMsg if ErrorCode != 0] + [业务数据 if ErrorCode == 0]
// ErrorCode=0表示成功，非0表示错误
// 成功时包含业务数据：Group（如果需要群验证）或 Permission + PermissionExpiration（如果激活成功）
// 失败时只包含ErrorMsg
type CDKeyActivateResponse struct {
	ErrorCode            uint32 // 错误码
	ErrorMsg             string // 错误消息（仅在ErrorCode != 0时包含）
	Group                *int64 // 需要加入的QQ群号（仅在需要群验证时包含）
	Permission           *uint8 // 更新后的权限级别（仅在激活成功且不需要群验证时包含）
	PermissionExpiration *int64 // 更新后的权限过期时间（仅在激活成功且不需要群验证时包含）：-1=永久，0=无权限，>0=具体时间戳
}

// 获取活动信息请求消息体结构
type GetActivityInfoRequest struct {
	Token string // Token
}

// 获取活动信息响应消息体结构
// 字段顺序：ErrorCode + [ErrorMsg if ErrorCode != 0] + [业务数据 if ErrorCode == 0]
type GetActivityInfoResponse struct {
	ErrorCode        uint32 // 错误码
	ErrorMsg         string // 错误消息（仅在ErrorCode != 0时包含）
	IsActive         bool   // 活动是否进行中
	StartTime        uint64 // 活动开始时间（Unix时间戳）
	EndTime          uint64 // 活动结束时间（Unix时间戳）
	SignInResumeTime uint64 // 签到权限恢复时间（Unix时间戳）
	Title            string // 活动标题
	Description      string // 活动描述
	Message          string // 给用户的消息
	RemainingDays    uint32 // 剩余天数（可选）
	RemainingHours   uint32 // 剩余小时数（可选）
}

// 字符串编解码辅助函数

// WriteString 将字符串写入字节数组，使用长度前缀格式（4字节长度+字符串内容）
// 注意：使用小端序以匹配C#客户端的BinaryWriter
// 不添加\0字符结束符，保持与客户端ReadString的兼容性
func WriteString(buf []byte, offset int, value string) int {
	// 写入字符串长度（4字节，小端序）
	binary.LittleEndian.PutUint32(buf[offset:], uint32(len(value)))
	offset += 4

	// 写入字符串内容
	copy(buf[offset:], value)
	offset += len(value)

	return offset
}

// ReadString 从字节数组读取字符串，使用长度前缀格式（4字节长度+字符串内容）
// 注意：使用小端序以匹配C#客户端的BinaryReader
// 自动处理客户端可能添加的\0字符结束符
func ReadString(data []byte, offset int) (string, int, error) {
	// 检查是否有足够的数据读取长度
	if offset+4 > len(data) {
		return "", offset, fmt.Errorf("insufficient data for string length at offset %d", offset)
	}

	// 读取字符串长度（小端序）
	length := binary.LittleEndian.Uint32(data[offset:])
	offset += 4

	// 检查是否有足够的数据读取字符串内容
	if offset+int(length) > len(data) {
		return "", offset - 4, fmt.Errorf("insufficient data for string content at offset %d: need %d bytes", offset, length)
	}

	// 读取字符串内容
	stringBytes := data[offset : offset+int(length)]

	// 移除可能存在的\0字符结束符
	// 客户端序列化时可能会添加\0字符，需要去除
	if len(stringBytes) > 0 && stringBytes[len(stringBytes)-1] == 0 {
		stringBytes = stringBytes[:len(stringBytes)-1]
	}

	value := string(stringBytes)
	offset += int(length)

	return value, offset, nil
}

// WriteBool 将布尔值写入字节数组（1字节）
func WriteBool(buf []byte, offset int, value bool) int {
	if value {
		buf[offset] = 1
	} else {
		buf[offset] = 0
	}
	return offset + 1
}

// ReadBool 从字节数组读取布尔值（1字节）
func ReadBool(data []byte, offset int) (bool, int, error) {
	if offset >= len(data) {
		return false, offset, fmt.Errorf("insufficient data for boolean at offset %d", offset)
	}

	value := data[offset] != 0
	return value, offset + 1, nil
}

// WriteUint8 将uint8写入字节数组（1字节）
func WriteUint8(buf []byte, offset int, value uint8) int {
	buf[offset] = value
	return offset + 1
}

// ReadUint8 从字节数组读取uint8（1字节）
func ReadUint8(data []byte, offset int) (uint8, int, error) {
	if offset >= len(data) {
		return 0, offset, fmt.Errorf("insufficient data for uint8 at offset %d", offset)
	}

	value := data[offset]
	return value, offset + 1, nil
}

// WriteUint32 将uint32写入字节数组（4字节，小端序）
// 注意：使用小端序以匹配C#客户端的BinaryWriter
func WriteUint32(buf []byte, offset int, value uint32) int {
	binary.LittleEndian.PutUint32(buf[offset:], value)
	return offset + 4
}

// ReadUint32 从字节数组读取uint32（4字节，小端序）
// 注意：使用小端序以匹配C#客户端的BinaryReader
func ReadUint32(data []byte, offset int) (uint32, int, error) {
	if offset+4 > len(data) {
		return 0, offset, fmt.Errorf("insufficient data for uint32 at offset %d", offset)
	}

	value := binary.LittleEndian.Uint32(data[offset:])
	return value, offset + 4, nil
}

// WriteUint64 将uint64写入字节数组（8字节，小端序）
// 注意：使用小端序以匹配C#客户端的BinaryWriter
func WriteUint64(buf []byte, offset int, value uint64) int {
	binary.LittleEndian.PutUint64(buf[offset:], value)
	return offset + 8
}

// WriteInt64 将int64写入字节数组（8字节，小端序）
// 注意：使用小端序以匹配C#客户端的BinaryWriter
func WriteInt64(buf []byte, offset int, value int64) int {
	binary.LittleEndian.PutUint64(buf[offset:], uint64(value))
	return offset + 8
}

// ReadUint64 从字节数组读取uint64（8字节，小端序）
// 注意：使用小端序以匹配C#客户端的BinaryReader
func ReadUint64(data []byte, offset int) (uint64, int, error) {
	if offset+8 > len(data) {
		return 0, offset, fmt.Errorf("insufficient data for uint64 at offset %d", offset)
	}

	value := binary.LittleEndian.Uint64(data[offset:])
	return value, offset + 8, nil
}

// ReadInt64 从字节数组读取int64（8字节，小端序）
// 注意：使用小端序以匹配C#客户端的BinaryReader
func ReadInt64(data []byte, offset int) (int64, int, error) {
	if offset+8 > len(data) {
		return 0, offset, fmt.Errorf("insufficient data for int64 at offset %d", offset)
	}

	value := int64(binary.LittleEndian.Uint64(data[offset:]))
	return value, offset + 8, nil
}

// 解码登录请求
func DecodeLoginRequest(data []byte) (*LoginRequest, error) {
	req := &LoginRequest{}
	offset := 0
	var err error

	// 按固定顺序解码字段
	req.QQNumber, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read QQNumber: %w", err)
	}

	req.DeviceFingerprint, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read DeviceFingerprint: %w", err)
	}

	return req, nil
}

// 编码登录响应
func EncodeLoginResponse(resp *LoginResponse) []byte {
	// 计算所需缓冲区大小
	size := 4 // ErrorCode

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		size += 4 + len(resp.ErrorMsg) // ErrorMsg
	}

	// 只有在成功时才包含业务数据
	if resp.ErrorCode == 0 {
		size += 4 + len(resp.Token) + // Token
			8 + // UID
			1 + // Permission (uint8)
			8 // PermissionExpiration (int64)
	}

	buf := make([]byte, size)
	offset := 0

	// 按固定顺序编码字段
	offset = WriteUint32(buf, offset, resp.ErrorCode)

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		offset = WriteString(buf, offset, resp.ErrorMsg)
	} else {
		// 成功时包含业务数据
		offset = WriteString(buf, offset, resp.Token)
		offset = WriteUint64(buf, offset, resp.UID)
		offset = WriteUint8(buf, offset, resp.Permission)
		_ = WriteInt64(buf, offset, resp.PermissionExpiration)
	}

	return buf
}

// 解码心跳请求
func DecodeHeartbeatRequest(data []byte) (*HeartbeatRequest, error) {
	req := &HeartbeatRequest{}
	offset := 0
	var err error

	// 解码Token字段
	req.Token, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Token: %w", err)
	}

	return req, nil
}

// 编码注销请求
func EncodeLogoutRequest(req *LogoutRequest) []byte {
	// 计算所需缓冲区大小
	size := 4 + len(req.Token)

	buf := make([]byte, size)
	offset := 0

	// 编码Token字段
	offset = WriteString(buf, offset, req.Token)

	return buf
}

// 解码注销请求
func DecodeLogoutRequest(data []byte) (*LogoutRequest, error) {
	req := &LogoutRequest{}
	offset := 0
	var err error

	// 解码Token字段
	req.Token, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Token: %w", err)
	}

	return req, nil
}

// 编码注销响应
func EncodeLogoutResponse(resp *LogoutResponse) []byte {
	// 计算所需缓冲区大小
	size := 4 // ErrorCode
	if resp.ErrorCode != 0 {
		size += 4 + len(resp.ErrorMsg) // ErrorMsg长度前缀 + ErrorMsg内容
	}

	buf := make([]byte, size)
	offset := 0

	// 编码ErrorCode字段
	binary.LittleEndian.PutUint32(buf[offset:], resp.ErrorCode)
	offset += 4

	// 如果有错误，编码ErrorMsg字段
	if resp.ErrorCode != 0 {
		offset = WriteString(buf, offset, resp.ErrorMsg)
	}

	return buf
}

// 解码注销响应
func DecodeLogoutResponse(data []byte) (*LogoutResponse, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("insufficient data for LogoutResponse: need at least 4 bytes, got %d", len(data))
	}

	resp := &LogoutResponse{}
	offset := 0

	// 解码ErrorCode字段
	resp.ErrorCode = binary.LittleEndian.Uint32(data[offset:])
	offset += 4

	// 如果有错误，解码ErrorMsg字段
	if resp.ErrorCode != 0 {
		var err error
		resp.ErrorMsg, offset, err = ReadString(data, offset)
		if err != nil {
			return nil, fmt.Errorf("failed to read ErrorMsg: %w", err)
		}
	}

	return resp, nil
}

// 解码签到抽奖请求
func DecodeLuckyDrawRequest(data []byte) (*LuckyDrawRequest, error) {
	req := &LuckyDrawRequest{}
	offset := 0
	var err error

	// 解码Token字段
	req.Token, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Token: %w", err)
	}

	return req, nil
}

// 编码签到抽奖响应
func EncodeLuckyDrawResponse(resp *LuckyDrawResponse) []byte {
	// 计算所需缓冲区大小
	size := 4 // ErrorCode

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		size += 4 + len(resp.ErrorMsg) // ErrorMsg
	}

	// 只有在成功时才包含业务数据
	if resp.ErrorCode == 0 {
		size += 4 + len(resp.Message) + // Message
			4 // Count

		// 如果包含权限信息，添加相应大小
		if resp.Permission != nil {
			size += 1 // Permission (uint8)
		}
		if resp.PermissionExpiration != nil {
			size += 8 // PermissionExpiration (int64)
		}
	}

	buf := make([]byte, size)
	offset := 0

	// 按固定顺序编码字段
	offset = WriteUint32(buf, offset, resp.ErrorCode)

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		offset = WriteString(buf, offset, resp.ErrorMsg)
	} else {
		// 成功时包含业务数据
		offset = WriteString(buf, offset, resp.Message)
		offset = WriteUint32(buf, offset, resp.Count)

		// 如果包含权限信息，编码权限字段
		if resp.Permission != nil {
			offset = WriteUint8(buf, offset, *resp.Permission)
		}
		if resp.PermissionExpiration != nil {
			offset = WriteInt64(buf, offset, *resp.PermissionExpiration)
		}
	}

	return buf
}

// 解码签到抽奖响应
func DecodeLuckyDrawResponse(data []byte) (*LuckyDrawResponse, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("insufficient data for LuckyDrawResponse: need at least 4 bytes, got %d", len(data))
	}

	resp := &LuckyDrawResponse{}
	offset := 0
	var err error

	// 解码ErrorCode字段
	resp.ErrorCode, offset, err = ReadUint32(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read ErrorCode: %w", err)
	}

	// 如果有错误，解码ErrorMsg字段
	if resp.ErrorCode != 0 {
		resp.ErrorMsg, offset, err = ReadString(data, offset)
		if err != nil {
			return nil, fmt.Errorf("failed to read ErrorMsg: %w", err)
		}
	} else {
		// 成功时解码业务数据
		resp.Message, offset, err = ReadString(data, offset)
		if err != nil {
			return nil, fmt.Errorf("failed to read Message: %w", err)
		}

		resp.Count, offset, err = ReadUint32(data, offset)
		if err != nil {
			return nil, fmt.Errorf("failed to read Count: %w", err)
		}
	}

	return resp, nil
}

// 解码签到状态请求
func DecodeLuckyStatusRequest(data []byte) (*LuckyStatusRequest, error) {
	req := &LuckyStatusRequest{}
	offset := 0
	var err error

	// 解码Token字段
	req.Token, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Token: %w", err)
	}

	return req, nil
}

// 编码签到状态响应
func EncodeLuckyStatusResponse(resp *LuckyStatusResponse) []byte {
	// 计算所需缓冲区大小
	size := 4 // ErrorCode

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		size += 4 + len(resp.ErrorMsg) // ErrorMsg
	}

	// 只有在成功时才包含业务数据
	if resp.ErrorCode == 0 {
		size += 4 + // TotalDays
			4 // AvailableCount
	}

	buf := make([]byte, size)
	offset := 0

	// 按固定顺序编码字段
	offset = WriteUint32(buf, offset, resp.ErrorCode)

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		offset = WriteString(buf, offset, resp.ErrorMsg)
	} else {
		// 成功时包含业务数据
		offset = WriteUint32(buf, offset, resp.TotalDays)
		offset = WriteUint32(buf, offset, resp.AvailableCount)
	}

	return buf
}

// 解码CDKEY激活请求
func DecodeCDKeyActivateRequest(data []byte) (*CDKeyActivateRequest, error) {
	req := &CDKeyActivateRequest{}
	offset := 0
	var err error

	// 解码Token字段
	req.Token, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Token: %w", err)
	}

	// 解码CDKey字段
	req.CDKey, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read CDKey: %w", err)
	}

	// 解码IsVerified字段
	req.IsVerified, offset, err = ReadBool(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read IsVerified: %w", err)
	}

	return req, nil
}

// 编码CDKEY激活响应
func EncodeCDKeyActivateResponse(resp *CDKeyActivateResponse) []byte {
	// 计算所需缓冲区大小
	size := 4 // ErrorCode

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		size += 4 + len(resp.ErrorMsg) // ErrorMsg
	}

	// 只有在成功时才包含业务数据
	if resp.ErrorCode == 0 {
		if resp.Group != nil {
			size += 8 // Group (int64)
		} else {
			// 激活成功时包含权限信息
			if resp.Permission != nil {
				size += 1 // Permission (uint8)
			}
			if resp.PermissionExpiration != nil {
				size += 8 // PermissionExpiration (int64)
			}
		}
	}

	buf := make([]byte, size)
	offset := 0

	// 按固定顺序编码字段
	offset = WriteUint32(buf, offset, resp.ErrorCode)

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		offset = WriteString(buf, offset, resp.ErrorMsg)
	} else {
		// 成功时包含业务数据
		if resp.Group != nil {
			// 需要群验证
			offset = WriteInt64(buf, offset, *resp.Group)
		} else {
			// 激活成功，包含权限信息
			if resp.Permission != nil {
				offset = WriteUint8(buf, offset, *resp.Permission)
			}
			if resp.PermissionExpiration != nil {
				offset = WriteInt64(buf, offset, *resp.PermissionExpiration)
			}
		}
	}

	return buf
}

// 编码获取活动信息请求
func EncodeGetActivityInfoRequest(req *GetActivityInfoRequest) []byte {
	// 计算总大小
	size := 4 + len(req.Token) // Token字符串长度前缀 + 内容

	buf := make([]byte, size)
	offset := 0

	// 编码Token字段
	offset = WriteString(buf, offset, req.Token)

	return buf
}

// 解码获取活动信息请求
func DecodeGetActivityInfoRequest(data []byte) (*GetActivityInfoRequest, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("insufficient data for GetActivityInfoRequest: need at least 4 bytes, got %d", len(data))
	}

	req := &GetActivityInfoRequest{}
	offset := 0
	var err error

	// 解码Token字段
	req.Token, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Token: %w", err)
	}

	return req, nil
}

// 编码获取活动信息响应
func EncodeGetActivityInfoResponse(resp *GetActivityInfoResponse) []byte {
	// 计算总大小
	size := 4 // ErrorCode

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		size += 4 + len(resp.ErrorMsg) // ErrorMsg字符串长度前缀 + 内容
	} else {
		// 成功时包含业务数据
		size += 1                         // IsActive (bool)
		size += 8                         // StartTime (uint64)
		size += 8                         // EndTime (uint64)
		size += 8                         // SignInResumeTime (uint64)
		size += 4 + len(resp.Title)       // Title字符串长度前缀 + 内容
		size += 4 + len(resp.Description) // Description字符串长度前缀 + 内容
		size += 4 + len(resp.Message)     // Message字符串长度前缀 + 内容
		size += 4                         // RemainingDays (uint32)
		size += 4                         // RemainingHours (uint32)
	}

	buf := make([]byte, size)
	offset := 0

	// 按固定顺序编码字段
	offset = WriteUint32(buf, offset, resp.ErrorCode)

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		offset = WriteString(buf, offset, resp.ErrorMsg)
	} else {
		// 成功时包含业务数据
		offset = WriteBool(buf, offset, resp.IsActive)
		offset = WriteUint64(buf, offset, resp.StartTime)
		offset = WriteUint64(buf, offset, resp.EndTime)
		offset = WriteUint64(buf, offset, resp.SignInResumeTime)
		offset = WriteString(buf, offset, resp.Title)
		offset = WriteString(buf, offset, resp.Description)
		offset = WriteString(buf, offset, resp.Message)
		offset = WriteUint32(buf, offset, resp.RemainingDays)
		offset = WriteUint32(buf, offset, resp.RemainingHours)
	}

	return buf
}
