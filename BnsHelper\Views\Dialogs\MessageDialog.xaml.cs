using HandyControl.Controls;
using HandyControl.Interactivity;
using HandyControl.Tools.Extension;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels.Dialogs;

namespace Xylia.Preview.UI.Views.Dialogs;

[DesignTimeVisible(false)]
public partial class MessageDialog
{
    #region Fields
    private readonly MessageDialogViewModel _viewModel;
    #endregion

    #region Constructor
    public MessageDialog()
    {
        try
        {
            InitializeComponent();
            CommandBindings.Add(new CommandBinding(ControlCommands.Close, CloseCommand));

            _viewModel = new MessageDialogViewModel();
            DataContext = _viewModel;
            _viewModel.CloseAction = () => CloseAction?.Invoke();

            Loaded += OnLoaded;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[MessageDialog] Constructor failed: {ex.Message}");
            Debug.WriteLine($"[MessageDialog] Stack trace: {ex.StackTrace}");
            throw;
        }
    }

    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        try
        {
            // 延迟初始化图标，确保所有资源都已加载
            Dispatcher.BeginInvoke(() => _viewModel.UpdateIcon(IconContainer), DispatcherPriority.ApplicationIdle);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[MessageDialog] OnLoaded failed: {ex.Message}");
        }
    }
    #endregion

    #region Properties
    // 为了保持向后兼容性，保留这些属性但委托给ViewModel
    public string Message
    {
        get => _viewModel.Message ?? string.Empty;
        set => _viewModel.Message = value;
    }

    public string OkText
    {
        get => _viewModel.OkText;
        set => _viewModel.OkText = value;
    }

    public MessageBoxButton ButtonType
    {
        get => _viewModel.ButtonType;
        set => _viewModel.ButtonType = value;
    }

    public MessageBoxImage Icon
    {
        get => _viewModel.Icon;
        set => _viewModel.Icon = value;
    }

    public int AutoCloseMilliseconds
    {
        get => _viewModel.AutoCloseMilliseconds;
        set => _viewModel.AutoCloseMilliseconds = value;
    }
    #endregion

    #region Interface Implementation
    public MessageBoxResult Result => _viewModel.Result;
    public Action? CloseAction { get; set; }

    private void CloseCommand(object sender, RoutedEventArgs e)
    {
        _viewModel.Close();
    }
    #endregion

    #region Static Methods
    public static async Task<bool> ShowDialog(string? message, string? btnText = null, int autoCloseMilliseconds = 3000) =>
        await ShowDialog(message, btnText, MessageBoxButton.OK, MessageBoxImage.Information, autoCloseMilliseconds) == MessageBoxResult.OK;

    public static async Task<MessageBoxResult> ShowDialog(string? message, string? btnText, MessageBoxButton button, MessageBoxImage icon = MessageBoxImage.Information, int autoCloseMilliseconds = 0)
    {
        try
        {
            // 确保主窗口处于活动状态，这是 HandyControl Dialog 正常显示的关键
            Application.Current.MainWindow?.Activate();

            // 添加超时机制防止无限等待
            Debug.WriteLine($"[MessageDialog] Creating dialog with autoCloseMilliseconds = {autoCloseMilliseconds}");
            var dialog = new MessageDialog()
            {
                Message = message,
                ButtonType = button,
                Icon = icon,
                AutoCloseMilliseconds = autoCloseMilliseconds,
                OkText = btnText ?? StringHelper.Get("Text.Ok")
            };

            // 设置超时，防止无限等待
            var timeoutTask = Task.Delay(15000);
            var dialogTask = Dialog.Show(dialog).GetResultAsync<MessageBoxResult>();

            var completedTask = await Task.WhenAny(dialogTask, timeoutTask);
            if (completedTask == timeoutTask)
            {
                dialog.CloseAction?.Invoke();
                return MessageBoxResult.Cancel;
            }

            return await dialogTask;
        }
        catch (Exception ex)
        {
            // 如果对话框显示失败，记录错误并返回默认值
            Debug.WriteLine($"MessageDialog.ShowDialog failed: {ex.Message}");
            return button == MessageBoxButton.OK ? MessageBoxResult.OK : MessageBoxResult.Cancel;
        }
    }
    #endregion
}
